<!DOCTYPE html>
<html>
<head>
    <title>Emergency Order Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        input { padding: 8px; margin: 5px; width: 200px; }
        pre { background: #f8f9fa; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚨 Emergency Order Processing Test</h1>
        
        <div class="status info">
            <strong>Instructions:</strong><br>
            1. Make sure your development server is running (npm run dev)<br>
            2. Enter an order ID below<br>
            3. Click "Test Connection" first<br>
            4. If connection works, click "Process Order"
        </div>

        <div>
            <label>Order ID:</label>
            <input type="text" id="orderId" value="5920323403859" placeholder="Enter order ID">
            <br>
            <button onclick="testConnection()">🔍 Test Connection</button>
            <button onclick="processOrder()">📦 Process Order</button>
            <button onclick="clearResults()">🗑️ Clear</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        const results = document.getElementById('results');
        
        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        async function testConnection() {
            log('🔍 Testing connection to app...', 'info');
            
            const endpoints = [
                '/api/health',
                '/api/process-orders',
                '/resources/bulk-order-details'
            ];
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(endpoint, {
                        method: endpoint === '/api/health' ? 'GET' : 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: endpoint === '/api/health' ? undefined : JSON.stringify({ test: true })
                    });
                    
                    if (response.ok) {
                        log(`✅ ${endpoint} - Working (${response.status})`, 'success');
                    } else {
                        log(`⚠️ ${endpoint} - Status ${response.status}`, 'error');
                    }
                } catch (error) {
                    log(`❌ ${endpoint} - Failed: ${error.message}`, 'error');
                }
            }
        }

        async function processOrder() {
            const orderId = document.getElementById('orderId').value.trim();
            
            if (!orderId) {
                log('❌ Please enter an order ID', 'error');
                return;
            }
            
            log(`🚀 Processing order: ${orderId}`, 'info');
            
            const endpoints = ['/api/process-orders', '/resources/bulk-order-details'];
            
            for (const endpoint of endpoints) {
                try {
                    log(`📡 Trying ${endpoint}...`, 'info');
                    
                    const response = await fetch(endpoint, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ orderIds: [orderId] })
                    });
                    
                    const data = await response.json();
                    
                    if (response.ok) {
                        log(`✅ Success via ${endpoint}!`, 'success');
                        log(`<pre>${JSON.stringify(data, null, 2)}</pre>`, 'success');
                        return;
                    } else {
                        log(`❌ ${endpoint} failed: ${data.error || 'Unknown error'}`, 'error');
                    }
                } catch (error) {
                    log(`❌ ${endpoint} error: ${error.message}`, 'error');
                }
            }
            
            log('❌ All endpoints failed. Please restart your development server.', 'error');
        }

        function clearResults() {
            results.innerHTML = '';
        }

        // Auto-test connection on load
        window.onload = () => {
            log('🚀 Emergency test page loaded. Testing connection...', 'info');
            setTimeout(testConnection, 1000);
        };
    </script>
</body>
</html>
